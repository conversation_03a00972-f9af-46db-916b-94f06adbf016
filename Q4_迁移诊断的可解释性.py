import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.tree import DecisionTreeClassifier, export_text, plot_tree
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.inspection import permutation_importance
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
import os

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'STHeiti', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class RealDataTransferInterpretability:
    def __init__(self, source_data_path='processed_data_mixed_fs'):
        self.source_data_path = source_data_path
        self.source_features = None
        self.source_labels = None
        self.target_features = None
        self.target_labels = None
        self.feature_names = None
        self.output_dir = os.path.join(os.getcwd(), "图片Q4")

    def save_figure(self, fig, filename):
        """Persist figure into 图片Q4 directory."""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"图像已保存: {filepath}")

    def load_real_data(self):
        """加载真实的轴承数据集"""
        print("=" * 60)
        print("加载真实轴承数据集")
        print("=" * 60)

        # 加载特征数据
        feature_file = f"{self.source_data_path}/extracted_features.csv"
        if not os.path.exists(feature_file):
            raise FileNotFoundError(f"特征文件不存在: {feature_file}")

        df = pd.read_csv(feature_file)
        print(f"加载数据形状: {df.shape}")
        print(f"故障类型分布:\n{df['fault_type'].value_counts()}")

        # 排除非特征列
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        X = df[feature_cols].values
        y = df['fault_type'].values
        self.feature_names = feature_cols

        print(f"特征数量: {len(feature_cols)}")
        print(f"样本数量: {len(X)}")

        if 'sensor_type' in df.columns:
            print("\n按传感器类型分割源域和目标域...")
            sensor_types = df['sensor_type'].unique()
            if len(sensor_types) >= 2:
                source_sensor = sensor_types[0]
                target_sensor = sensor_types[1]

                source_mask = df['sensor_type'] == source_sensor
                target_mask = df['sensor_type'] == target_sensor

                self.source_features = X[source_mask]
                self.source_labels = y[source_mask]
                self.target_features = X[target_mask]
                self.target_labels = y[target_mask]

                print(f"源域 ({source_sensor}): {len(self.source_features)} 样本")
                print(f"目标域 ({target_sensor}): {len(self.target_features)} 样本")

                return self.source_features, self.target_features

        if 'rpm' in df.columns:
            print("\n按转速分割源域和目标域...")
            rpms = sorted(df['rpm'].unique())
            if len(rpms) >= 2:
                source_rpms = rpms[:len(rpms) // 2]
                target_rpms = rpms[len(rpms) // 2:]

                source_mask = df['rpm'].isin(source_rpms)
                target_mask = df['rpm'].isin(target_rpms)

                self.source_features = X[source_mask]
                self.source_labels = y[source_mask]
                self.target_features = X[target_mask]
                self.target_labels = y[target_mask]

                print(f"源域 (RPM: {source_rpms}): {len(self.source_features)} 样本")
                print(f"目标域 (RPM: {target_rpms}): {len(self.target_features)} 样本")

                return self.source_features, self.target_features

        print("\n按分层抽样分割源域和目标域...")
        X_source, X_target, y_source, y_target = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )

        self.source_features = X_source
        self.source_labels = y_source
        self.target_features = X_target
        self.target_labels = y_target

        print(f"源域: {len(self.source_features)} 样本")
        print(f"目标域: {len(self.target_features)} 样本")

        print(f"\n源域类别分布:")
        for label in np.unique(y_source):
            count = np.sum(y_source == label)
            print(f"  {label}: {count} ({count / len(y_source) * 100:.1f}%)")

        print(f"\n目标域类别分布:")
        for label in np.unique(y_target):
            count = np.sum(y_target == label)
            print(f"  {label}: {count} ({count / len(y_target) * 100:.1f}%)")

        return self.source_features, self.target_features

    def train_interpretable_models(self):
        """训练可解释的模型"""
        print("\n" + "=" * 60)
        print("训练可解释模型")
        print("=" * 60)

        # 数据预处理
        scaler = StandardScaler()
        source_scaled = scaler.fit_transform(self.source_features)
        target_scaled = scaler.transform(self.target_features)

        models = {}
        accuracies = {}

        print("\n1. 训练决策树模型...")
        dt_model = DecisionTreeClassifier(
            max_depth=8,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42
        )
        dt_model.fit(source_scaled, self.source_labels)
        dt_pred = dt_model.predict(target_scaled)
        dt_acc = accuracy_score(self.target_labels, dt_pred)

        models['decision_tree'] = dt_model
        accuracies['decision_tree'] = dt_acc
        print(f"决策树准确率: {dt_acc:.4f}")

        print("\n2. 训练逻辑回归模型...")
        lr_model = LogisticRegression(
            random_state=42,
            max_iter=2000,
            C=1.0,
            class_weight='balanced'
        )
        lr_model.fit(source_scaled, self.source_labels)
        lr_pred = lr_model.predict(target_scaled)
        lr_acc = accuracy_score(self.target_labels, lr_pred)

        models['logistic_regression'] = lr_model
        accuracies['logistic_regression'] = lr_acc
        print(f"逻辑回归准确率: {lr_acc:.4f}")

        print("\n3. 训练随机森林模型...")
        rf_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=12,
            random_state=42,
            class_weight='balanced'
        )
        rf_model.fit(source_scaled, self.source_labels)
        rf_pred = rf_model.predict(target_scaled)
        rf_acc = accuracy_score(self.target_labels, rf_pred)

        models['random_forest'] = rf_model
        accuracies['random_forest'] = rf_acc
        print(f"随机森林准确率: {rf_acc:.4f}")

        self.models = models
        self.accuracies = accuracies
        self.scaler = scaler
        self.source_scaled = source_scaled
        self.target_scaled = target_scaled

        return models, accuracies

    def analyze_ex_ante_interpretability(self):
        """事前可解释性分析"""
        print("\n" + "=" * 60)
        print("事前可解释性分析")
        print("=" * 60)

        # 1. 决策树规则提取
        print("\n1. 决策树规则分析...")
        dt_model = self.models['decision_tree']

        tree_rules = export_text(
            dt_model,
            feature_names=[f"F{i}" for i in range(len(self.feature_names))],
            max_depth=5
        )

        print("关键决策规则（前5层）:")
        print(tree_rules[:800] + "...")

        print("\n2. 线性模型特征权重分析...")
        lr_model = self.models['logistic_regression']

        feature_importance_by_class = {}
        for i, class_name in enumerate(lr_model.classes_):
            weights = lr_model.coef_[i]
            top_indices = np.argsort(np.abs(weights))[-10:]
            feature_importance_by_class[class_name] = {
                'indices': top_indices,
                'weights': weights[top_indices],
                'feature_names': [self.feature_names[idx] for idx in top_indices]
            }

        print("\n3. 随机森林特征重要性分析...")
        rf_model = self.models['random_forest']
        feature_importances = rf_model.feature_importances_

        top_feature_indices = np.argsort(feature_importances)[-20:]
        top_features = [(self.feature_names[i], feature_importances[i]) for i in top_feature_indices]

        print("前10个最重要特征:")
        for name, importance in top_features[-10:]:
            print(f"  {name}: {importance:.4f}")

        print("\n4. 特征物理意义分析...")
        feature_categories = self._categorize_features_by_physics()

        for category, features in feature_categories.items():
            if features:
                print(f"  {category}: {len(features)} 个特征")

        self._visualize_ex_ante_results(feature_importance_by_class,
                                        feature_importances, feature_categories)

        return {
            'tree_rules': tree_rules,
            'feature_importance_by_class': feature_importance_by_class,
            'rf_feature_importances': feature_importances,
            'feature_categories': feature_categories
        }

    def analyze_transfer_process(self):
        """迁移过程可解释性分析"""
        print("\n" + "=" * 60)
        print("迁移过程可解释性分析")
        print("=" * 60)

        source_scaled = self.source_scaled
        target_scaled = self.target_scaled

        print("\n1. 分析源域和目标域特征分布差异...")
        distribution_differences = self._analyze_distribution_differences(
            source_scaled, target_scaled
        )

        print("\n2. 特征空间可视化...")
        visualization_results = self._visualize_feature_spaces(
            source_scaled, target_scaled,
            self.source_labels, self.target_labels
        )

        print("\n3. 类别中心迁移分析...")
        center_analysis = self._analyze_class_centers(
            source_scaled, target_scaled,
            self.source_labels, self.target_labels
        )

        return {
            'distribution_differences': distribution_differences,
            'visualization_results': visualization_results,
            'center_analysis': center_analysis
        }

    def analyze_ex_post_interpretability(self):
        """事后可解释性分析"""
        print("\n" + "=" * 60)
        print("事后可解释性分析")
        print("=" * 60)

        print("\n1. 排列重要性分析...")
        perm_results = {}

        for model_name, model in self.models.items():
            print(f"   分析 {model_name}...")
            perm_importance = permutation_importance(
                model, self.target_scaled, self.target_labels,
                n_repeats=5, random_state=42
            )
            perm_results[model_name] = perm_importance

        print("\n2. 预测结果分析...")
        prediction_analysis = self._analyze_predictions()

        print("\n3. 基于故障机理的解释...")
        mechanism_explanation = self._explain_by_mechanism(perm_results)

        self._visualize_ex_post_results(perm_results, prediction_analysis,
                                        mechanism_explanation)

        return {
            'permutation_importance': perm_results,
            'prediction_analysis': prediction_analysis,
            'mechanism_explanation': mechanism_explanation
        }

    def _categorize_features_by_physics(self):
        """按物理机理对特征分类"""
        categories = {
            '时域统计特征': [],
            '频域特征': [],
            '时频域特征': [],
            '故障特征频率': [],
            '包络特征': [],
            '其他特征': []
        }

        for feature in self.feature_names:
            feature_lower = feature.lower()

            if any(keyword in feature_lower for keyword in
                   ['mean', 'std', 'rms', 'peak', 'kurtosis', 'skewness', 'variance']):
                categories['时域统计特征'].append(feature)
            elif any(keyword in feature_lower for keyword in
                     ['spectral', 'frequency', 'fft', 'power']):
                categories['频域特征'].append(feature)
            elif any(keyword in feature_lower for keyword in
                     ['wavelet', 'stft', 'cwt']):
                categories['时频域特征'].append(feature)
            elif any(keyword in feature_lower for keyword in
                     ['bpfo', 'bpfi', 'bsf', 'fr', 'fundamental']):
                categories['故障特征频率'].append(feature)
            elif any(keyword in feature_lower for keyword in
                     ['envelope', 'hilbert', 'demod']):
                categories['包络特征'].append(feature)
            else:
                categories['其他特征'].append(feature)

        # 移除空类别
        return {k: v for k, v in categories.items() if v}

    def _analyze_distribution_differences(self, source, target):
        """分析源域和目标域的分布差异"""
        from scipy import stats

        differences = []

        for i in range(source.shape[1]):
            source_feat = source[:, i]
            target_feat = target[:, i]

            # KS检验
            ks_stat, ks_pvalue = stats.ks_2samp(source_feat, target_feat)

            # 均值和方差差异
            mean_diff = abs(np.mean(source_feat) - np.mean(target_feat))
            var_ratio = np.var(target_feat) / (np.var(source_feat) + 1e-8)

            differences.append({
                'feature_idx': i,
                'ks_statistic': ks_stat,
                'ks_pvalue': ks_pvalue,
                'mean_difference': mean_diff,
                'variance_ratio': var_ratio
            })

        return differences

    def _visualize_feature_spaces(self, source, target, source_labels, target_labels):
        """可视化特征空间"""
        # PCA降维
        all_data = np.vstack([source, target])
        pca = PCA(n_components=2)
        all_pca = pca.fit_transform(all_data)

        source_pca = all_pca[:len(source)]
        target_pca = all_pca[len(source):]

        # t-SNE降维
        n_samples = min(1000, len(all_data))
        subset_indices = np.random.choice(len(all_data), n_samples, replace=False)
        subset_data = all_data[subset_indices]

        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, n_samples - 1))
        subset_tsne = tsne.fit_transform(subset_data)

        # 可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # PCA - 按域分布
        axes[0, 0].scatter(source_pca[:, 0], source_pca[:, 1],
                           alpha=0.6, label='Source', s=20, c='blue')
        axes[0, 0].scatter(target_pca[:, 0], target_pca[:, 1],
                           alpha=0.8, label='Target', s=20, c='red')
        axes[0, 0].set_title('PCA: 域分布')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # PCA - 按故障类型
        fault_types = np.unique(np.concatenate([source_labels, target_labels]))
        colors = ['green', 'red', 'blue', 'orange', 'purple']

        for i, fault_type in enumerate(fault_types):
            source_mask = source_labels == fault_type
            target_mask = target_labels == fault_type

            if np.any(source_mask):
                axes[0, 1].scatter(source_pca[source_mask, 0], source_pca[source_mask, 1],
                                   alpha=0.6, label=f'{fault_type}(S)',
                                   s=20, c=colors[i % len(colors)], marker='o')
            if np.any(target_mask):
                axes[0, 1].scatter(target_pca[target_mask, 0], target_pca[target_mask, 1],
                                   alpha=0.8, label=f'{fault_type}(T)',
                                   s=20, c=colors[i % len(colors)], marker='^')

        axes[0, 1].set_title('PCA: 故障类型分布')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # t-SNE可视化
        source_subset_mask = subset_indices < len(source)
        target_subset_mask = subset_indices >= len(source)

        axes[1, 0].scatter(subset_tsne[source_subset_mask, 0],
                           subset_tsne[source_subset_mask, 1],
                           alpha=0.6, label='Source', s=20, c='blue')
        axes[1, 0].scatter(subset_tsne[target_subset_mask, 0],
                           subset_tsne[target_subset_mask, 1],
                           alpha=0.8, label='Target', s=20, c='red')
        axes[1, 0].set_title('t-SNE: 域分布')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        # 特征重要性对比
        rf_importance = self.models['random_forest'].feature_importances_
        top_indices = np.argsort(rf_importance)[-15:]

        axes[1, 1].barh(range(len(top_indices)), rf_importance[top_indices],
                        alpha=0.7, color='steelblue')
        axes[1, 1].set_yticks(range(len(top_indices)))
        axes[1, 1].set_yticklabels([f"F{i}" for i in top_indices], fontsize=8)
        axes[1, 1].set_title('随机森林特征重要性')
        axes[1, 1].set_xlabel('重要性')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        self.save_figure(fig, 'feature_spaces_analysis.png')

        return {
            'pca_explained_variance': pca.explained_variance_ratio_,
            'source_pca': source_pca,
            'target_pca': target_pca
        }

    def _analyze_class_centers(self, source, target, source_labels, target_labels):
        """分析类别中心迁移"""
        center_analysis = {}

        fault_types = np.unique(source_labels)

        for fault_type in fault_types:
            if fault_type in target_labels:
                source_mask = source_labels == fault_type
                target_mask = target_labels == fault_type

                source_center = np.mean(source[source_mask], axis=0)
                target_center = np.mean(target[target_mask], axis=0)

                # 计算中心间距离
                euclidean_dist = np.linalg.norm(source_center - target_center)
                cosine_dist = 1 - np.dot(source_center, target_center) / (
                        np.linalg.norm(source_center) * np.linalg.norm(target_center) + 1e-8
                )

                center_analysis[fault_type] = {
                    'euclidean_distance': euclidean_dist,
                    'cosine_distance': cosine_dist,
                    'source_center': source_center,
                    'target_center': target_center
                }

        return center_analysis

    def _analyze_predictions(self):
        """分析预测结果"""
        prediction_analysis = {}

        for model_name, model in self.models.items():
            pred = model.predict(self.target_scaled)
            pred_proba = model.predict_proba(self.target_scaled)

            # 准确率
            accuracy = accuracy_score(self.target_labels, pred)

            # 混淆矩阵
            cm = confusion_matrix(self.target_labels, pred)

            # 预测置信度
            max_proba = np.max(pred_proba, axis=1)

            prediction_analysis[model_name] = {
                'accuracy': accuracy,
                'predictions': pred,
                'probabilities': pred_proba,
                'confusion_matrix': cm,
                'confidence': max_proba
            }

        return prediction_analysis

    def _explain_by_mechanism(self, perm_results):
        """基于故障机理解释"""
        feature_categories = self._categorize_features_by_physics()
        mechanism_explanation = {}

        # 对每个模型分析
        for model_name, perm_importance in perm_results.items():
            category_importance = {}

            for category, features in feature_categories.items():
                if features:
                    feature_indices = [self.feature_names.index(f) for f in features
                                       if f in self.feature_names]
                    if feature_indices:
                        avg_importance = np.mean(
                            perm_importance.importances_mean[feature_indices]
                        )
                        category_importance[category] = avg_importance

            mechanism_explanation[model_name] = category_importance

        return mechanism_explanation

    def _visualize_ex_ante_results(self, feature_importance_by_class,
                                   rf_importances, feature_categories):
        """可视化事前可解释性结果"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 决策树可视化
        dt_model = self.models['decision_tree']
        plot_tree(dt_model, ax=axes[0, 0],
                  feature_names=[f"F{i}" for i in range(len(self.feature_names))],
                  class_names=sorted(np.unique(self.source_labels)),
                  filled=True, rounded=True, fontsize=6, max_depth=3)
        axes[0, 0].set_title(f'决策树 (Acc={self.accuracies["decision_tree"]:.3f})')

        # 逻辑回归权重
        all_weights = []
        all_features = []
        for class_name, info in feature_importance_by_class.items():
            all_weights.extend(info['weights'])
            all_features.extend([f"{f}({class_name})" for f in
                                 [f"F{i}" for i in info['indices']]])

        if all_weights:
            axes[0, 1].barh(range(len(all_weights)), all_weights,
                            color=['red' if w < 0 else 'blue' for w in all_weights],
                            alpha=0.7)
            axes[0, 1].set_yticks(range(len(all_weights)))
            axes[0, 1].set_yticklabels(all_features, fontsize=6)
            axes[0, 1].set_title(f'逻辑回归权重 (Acc={self.accuracies["logistic_regression"]:.3f})')

        # 随机森林特征重要性
        top_indices = np.argsort(rf_importances)[-15:]
        axes[0, 2].barh(range(len(top_indices)), rf_importances[top_indices],
                        alpha=0.7, color='green')
        axes[0, 2].set_yticks(range(len(top_indices)))
        axes[0, 2].set_yticklabels([f"F{i}" for i in top_indices], fontsize=8)
        axes[0, 2].set_title(f'随机森林重要性 (Acc={self.accuracies["random_forest"]:.3f})')

        # 模型性能对比
        model_names = list(self.accuracies.keys())
        accuracies = list(self.accuracies.values())
        bars = axes[1, 0].bar(model_names, accuracies, alpha=0.7,
                              color=['orange', 'blue', 'green'])
        axes[1, 0].set_ylabel('准确率')
        axes[1, 0].set_title('模型性能对比')
        axes[1, 0].tick_params(axis='x', rotation=45)

        for bar, acc in zip(bars, accuracies):
            axes[1, 0].text(bar.get_x() + bar.get_width() / 2, bar.get_height() + 0.01,
                            f'{acc:.3f}', ha='center', va='bottom')
        # 特征类型分布
        if feature_categories:
            categories = list(feature_categories.keys())
            counts = [len(features) for features in feature_categories.values()]

            axes[1, 1].pie(counts, labels=categories, autopct='%1.1f%%', startangle=90)
            axes[1, 1].set_title('特征类型分布')

        # 最佳模型混淆矩阵
        best_model_name = max(self.accuracies.keys(), key=lambda k: self.accuracies[k])
        best_model = self.models[best_model_name]
        best_pred = best_model.predict(self.target_scaled)

        cm = confusion_matrix(self.target_labels, best_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 2],
                    xticklabels=sorted(np.unique(self.target_labels)),
                    yticklabels=sorted(np.unique(self.target_labels)))
        axes[1, 2].set_title(f'混淆矩阵 ({best_model_name})')

        plt.tight_layout()
        self.save_figure(fig, 'ex_ante_interpretability.png')

    def _visualize_ex_post_results(self, perm_results, prediction_analysis,
                                   mechanism_explanation):
        """可视化事后可解释性结果"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 排列重要性对比
        best_model_name = max(self.accuracies.keys(), key=lambda k: self.accuracies[k])
        best_perm = perm_results[best_model_name]

        top_indices = np.argsort(best_perm.importances_mean)[-20:]
        axes[0, 0].barh(range(len(top_indices)), best_perm.importances_mean[top_indices],
                        xerr=best_perm.importances_std[top_indices],
                        alpha=0.7, color='steelblue')
        axes[0, 0].set_yticks(range(len(top_indices)))
        axes[0, 0].set_yticklabels([f"F{i}" for i in top_indices], fontsize=8)
        axes[0, 0].set_title(f'排列重要性 ({best_model_name})')

        # 故障机理重要性
        best_mechanism = mechanism_explanation[best_model_name]
        if best_mechanism:
            mechanisms = list(best_mechanism.keys())
            importances = list(best_mechanism.values())

            bars = axes[0, 1].bar(mechanisms, importances, alpha=0.7,
                                  color=['red', 'blue', 'green', 'orange', 'purple'])
            axes[0, 1].set_ylabel('平均重要性')
            axes[0, 1].set_title('机理重要性分析')
            axes[0, 1].tick_params(axis='x', rotation=45)

            for bar, imp in zip(bars, importances):
                axes[0, 1].text(bar.get_x() + bar.get_width() / 2, bar.get_height() + 0.0005,
                                f'{imp:.4f}', ha='center', va='bottom', fontsize=8)

        # 预测置信度分布
        best_pred_analysis = prediction_analysis[best_model_name]
        axes[0, 2].hist(best_pred_analysis['confidence'], bins=20, alpha=0.7,
                        color='lightgreen', edgecolor='black')
        axes[0, 2].set_xlabel('预测置信度')
        axes[0, 2].set_ylabel('频数')
        axes[0, 2].set_title('预测置信度分布')
        axes[0, 2].grid(True, alpha=0.3)

        # 模型性能对比
        model_accuracies = [prediction_analysis[name]['accuracy'] for name in self.models.keys()]
        axes[1, 0].bar(self.models.keys(), model_accuracies, alpha=0.7,
                       color=['orange', 'blue', 'green'])
        axes[1, 0].set_ylabel('准确率')
        axes[1, 0].set_title('模型准确率对比')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 类别预测准确率
        fault_types = sorted(np.unique(self.target_labels))
        class_accuracies = []

        for fault_type in fault_types:
            mask = self.target_labels == fault_type
            if np.any(mask):
                true_labels = self.target_labels[mask]
                pred_labels = best_pred_analysis['predictions'][mask]
                class_acc = accuracy_score(true_labels, pred_labels)
                class_accuracies.append(class_acc)
            else:
                class_accuracies.append(0)

        axes[1, 1].bar(fault_types, class_accuracies, alpha=0.7, color='lightcoral')
        axes[1, 1].set_ylabel('准确率')
        axes[1, 1].set_title('各类别预测准确率')
        axes[1, 1].tick_params(axis='x', rotation=45)

        # 特征重要性热图
        if len(self.models) > 1:
            importance_matrix = []
            for model_name in self.models.keys():
                perm = perm_results[model_name]
                importance_matrix.append(perm.importances_mean)

            importance_matrix = np.array(importance_matrix)
            top_feature_indices = np.argsort(importance_matrix.mean(axis=0))[-15:]

            sns.heatmap(importance_matrix[:, top_feature_indices],
                        xticklabels=[f"F{i}" for i in top_feature_indices],
                        yticklabels=list(self.models.keys()),
                        annot=True, fmt='.3f', cmap='viridis', ax=axes[1, 2])
            axes[1, 2].set_title('模型特征重要性对比')

        plt.tight_layout()
        self.save_figure(fig, 'ex_post_interpretability.png')

    def generate_final_report(self):
        """生成最终的可解释性报告"""
        print("\n" + "=" * 60)
        print("生成综合可解释性分析报告")
        print("=" * 60)

        # 获取最佳模型
        best_model_name = max(self.accuracies.keys(), key=lambda k: self.accuracies[k])
        best_accuracy = self.accuracies[best_model_name]

        # 生成报告
        report = [
            "# 基于真实数据的轴承故障诊断迁移学习可解释性分析报告",
            "=" * 80,
            "",
            "## 数据概况",
            f"- 源域样本数: {len(self.source_features)}",
            f"- 目标域样本数: {len(self.target_features)}",
            f"- 特征维度: {self.source_features.shape[1]}",
            f"- 故障类型: {sorted(np.unique(self.source_labels))}",
            "",
            "## 模型性能对比",
            ""
        ]

        for model_name, accuracy in self.accuracies.items():
            report.append(f"- {model_name}: {accuracy:.4f}")


        print("\n".join(report))
        return "\n".join(report)


def main():
    """主函数"""
    print("=" * 80)
    print("基于真实数据的迁移学习可解释性分析")
    print("=" * 80)

    try:
        # 初始化分析器
        analyzer = RealDataTransferInterpretability(
            source_data_path='processed_data_mixed_fs'
        )

        # 步骤1: 加载真实数据
        print("\n步骤1: 加载真实轴承数据")
        analyzer.load_real_data()

        # 步骤2: 训练可解释模型
        print("\n步骤2: 训练可解释模型")
        analyzer.train_interpretable_models()

        # 步骤3: 事前可解释性分析
        print("\n步骤3: 事前可解释性分析")
        analyzer.analyze_ex_ante_interpretability()

        # 步骤4: 迁移过程分析
        print("\n步骤4: 迁移过程可解释性分析")
        analyzer.analyze_transfer_process()

        # 步骤5: 事后可解释性分析
        print("\n步骤5: 事后可解释性分析")
        analyzer.analyze_ex_post_interpretability()

        # 步骤6: 生成最终报告
        print("\n步骤6: 生成综合报告")
        analyzer.generate_final_report()


    except FileNotFoundError as e:
        print(f" 文件未找到: {e}")
        print("请确保数据文件存在于指定路径")
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
