import scipy.io as sio
import numpy as np
import os
from datetime import datetime
import pandas as pd
from pathlib import Path


def analyze_mat_files(directory_path):
    """
    遍历目录结构并分析所有MAT文件的变量信息
    """
    # 存储所有分析结果
    results = []

    # 支持的MAT文件扩展名
    mat_extensions = {'.mat'}

    print("开始分析MAT文件...")
    print("=" * 80)

    # 遍历目录
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if any(file.endswith(ext) for ext in mat_extensions):
                file_path = os.path.join(root, file)

                try:
                    # 读取MAT文件
                    mat_data = sio.loadmat(file_path)

                    # 获取相对路径
                    relative_path = os.path.relpath(file_path, directory_path)

                    # 分析文件信息
                    file_info = os.stat(file_path)
                    file_size_kb = file_info.st_size / 1024

                    # 提取变量信息
                    variables = []
                    for key, value in mat_data.items():
                        if not key.startswith('__'):
                            var_info = {
                                'name': key,
                                'type': type(value).__name__,
                                'shape': str(value.shape) if hasattr(value, 'shape') else 'N/A',
                                'dtype': str(value.dtype) if hasattr(value, 'dtype') else 'N/A',
                                'size_kb': value.nbytes / 1024 if hasattr(value, 'nbytes') else 0
                            }
                            variables.append(var_info)

                    # 添加到结果
                    results.append({
                        'file_path': relative_path,
                        'file_size_kb': round(file_size_kb, 2),
                        'variable_count': len(variables),
                        'variables': variables,
                        'all_keys': list(mat_data.keys())
                    })

                    print(f"✓ 已分析: {relative_path} ({len(variables)}个变量)")

                except Exception as e:
                    print(f"✗ 分析失败: {relative_path} - 错误: {e}")
                    results.append({
                        'file_path': relative_path,
                        'error': str(e),
                        'file_size_kb': round(file_info.st_size / 1024, 2) if 'file_info' in locals() else 0
                    })

    return results


def generate_summary_report(results):
    """
    生成分析报告摘要
    """
    print("\n" + "=" * 80)
    print("MAT文件分析报告摘要")
    print("=" * 80)

    # 基本统计
    successful_analysis = [r for r in results if 'error' not in r]
    failed_analysis = [r for r in results if 'error' in r]

    print(f" 总文件数: {len(results)}")
    print(f" 成功分析: {len(successful_analysis)}")
    print(f" 分析失败: {len(failed_analysis)}")

    if successful_analysis:
        # 变量统计
        all_variables = []
        for result in successful_analysis:
            all_variables.extend([v['name'] for v in result['variables']])

        variable_counts = pd.Series(all_variables).value_counts()

        print(f"\n 变量统计:")
        print("-" * 40)
        for var_name, count in variable_counts.items():
            print(f"  {var_name}: {count}次出现")

        # 文件大小统计
        file_sizes = [r['file_size_kb'] for r in successful_analysis]
        print(f"\n 文件大小统计:")
        print(f"  平均大小: {np.mean(file_sizes):.2f} KB")
        print(f"  最小大小: {np.min(file_sizes):.2f} KB")
        print(f"  最大大小: {np.max(file_sizes):.2f} KB")
        print(f"  总大小: {np.sum(file_sizes):.2f} KB")

    # 显示失败的文件
    if failed_analysis:
        print(f"\n 分析失败的文件:")
        print("-" * 40)
        for failed in failed_analysis[:5]:  # 只显示前5个失败文件
            print(f"  {failed['file_path']}: {failed['error']}")
        if len(failed_analysis) > 5:
            print(f"  ... 还有 {len(failed_analysis) - 5} 个失败文件")


def analyze_variable_patterns(results):
    """
    分析变量模式
    """
    successful_results = [r for r in results if 'error' not in r]

    if not successful_results:
        return

    print(f"\n🔍 变量模式分析:")
    print("-" * 50)

    # 收集所有文件的变量名组合
    variable_patterns = {}
    for result in successful_results:
        var_names = tuple(sorted([v['name'] for v in result['variables']]))
        if var_names not in variable_patterns:
            variable_patterns[var_names] = []
        variable_patterns[var_names].append(result['file_path'])

    print(f"发现 {len(variable_patterns)} 种不同的变量组合模式:")

    for pattern_idx, (pattern, files) in enumerate(variable_patterns.items(), 1):
        print(f"\n模式 {pattern_idx}: {pattern}")
        print(f"   包含 {len(files)} 个文件")
        if len(files) <= 3:
            for file in files:
                print(f"     - {file}")
        else:
            print(f"     - {files[0]}")
            print(f"     - {files[1]}")
            print(f"     - ... 等 {len(files)} 个文件")


def save_detailed_report(results, output_file="mat_analysis_report.csv"):
    """
    保存详细分析报告到CSV文件
    """
    detailed_data = []

    for result in results:
        if 'error' in result:
            detailed_data.append({
                'file_path': result['file_path'],
                'status': '失败',
                'error': result['error'],
                'file_size_kb': result['file_size_kb'],
                'variable_names': 'N/A',
                'variable_count': 0
            })
        else:
            var_names = ', '.join([v['name'] for v in result['variables']])
            detailed_data.append({
                'file_path': result['file_path'],
                'status': '成功',
                'error': '无',
                'file_size_kb': result['file_size_kb'],
                'variable_names': var_names,
                'variable_count': result['variable_count']
            })

    df = pd.DataFrame(detailed_data)
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n 详细报告已保存到: {output_file}")


def sample_file_analysis(results, sample_count=3):
    """
    显示几个样本文件的详细分析
    """
    successful_results = [r for r in results if 'error' not in r]

    if not successful_results:
        return

    print(f"\n 样本文件详细分析 (显示前{sample_count}个):")
    print("=" * 60)

    for i, result in enumerate(successful_results[:sample_count]):
        print(f"\n 文件: {result['file_path']}")
        print(f" 大小: {result['file_size_kb']} KB")
        print(f" 变量数量: {result['variable_count']}")
        print("变量详情:")
        for var in result['variables']:
            print(f"  {var['name']}: {var['type']} {var['shape']} ({var['dtype']}) - {var['size_kb']:.2f} KB")
        print("-" * 40)


# 主程序
if __name__ == "__main__":
    # 替换为你的目录路径
    directory_path = r"F:\25_09_21_huaweibei\源域数据集"

    # 检查目录是否存在
    if not os.path.exists(directory_path):
        print(f"错误: 目录不存在: {directory_path}")
    else:
        # 分析所有MAT文件
        results = analyze_mat_files(directory_path)

        # 生成报告
        generate_summary_report(results)

        # 分析变量模式
        analyze_variable_patterns(results)

        # 显示样本分析
        sample_file_analysis(results)

        # 保存详细报告
        save_detailed_report(results)

        print("\n 分析完成！")